// models/Subcategory.js
import mongoose from "mongoose";

const SubcategorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: true, // e.g., "Languages & Frameworks", "Design Tools", "Databases"
  },
  profession: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Profession",
    required: true,
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
}, {
  timestamps: true, // adds createdAt and updatedAt fields
});

export default mongoose.model("Subcategory", SubcategorySchema);
