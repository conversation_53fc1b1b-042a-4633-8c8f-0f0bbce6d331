import rateLimit from "express-rate-limit";

// Login limiter: max 5 attempts every 15 mins
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 mins
  max: 5,
  message: {
    success: false,
    message: "Too many login attempts. Please try again later.",
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Forgot password limiter: max 3 attempts every 30 mins
const forgotPasswordLimiter = rateLimit({
  windowMs: 30 * 60 * 1000, // 30 mins
  max: 3,
  message: {
    success: false,
    message: "Too many password reset requests. Please try again later.",
  },
  standardHeaders: true,
  legacyHeaders: false,
});

export {
  loginLimiter,
  forgotPasswordLimiter,
};
