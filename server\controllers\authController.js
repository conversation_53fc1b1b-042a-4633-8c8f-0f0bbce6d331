import bcrypt from "bcrypt";
import crypto from "crypto";
import User from "../models/User.js";
import { sendEmail } from "../utils/mailer.js";
import jwt from "jsonwebtoken";
import {
  verifyEmailTemp,
  resetPasswordEmailTemp,
} from "../utils/emailTemplate.js";

export const registerUser = async (req, res) => {
  try {
    const { username, email, password } = req.body;

    if (!username || !email || !password) {
      return res
        .status(400)
        .json({ success: false, message: "All fields are required" });
    }

    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res
        .status(409)
        .json({ success: false, message: "Email or username already exists" });
    }

    const hashedPassword = await bcrypt.hash(password, 12);

    const emailVerifyToken = crypto.randomBytes(32).toString("hex");
    const emailVerifyExpires = Date.now() + 60 * 60 * 1000; // 1 hour

    const newUser = new User({
      username,
      email,
      password: hashedPassword,
      emailVerifyToken,
      emailVerifyExpires,
    });

    await newUser.save();

    // ✅ Construct email verification URL
    const verifyUrl = `${process.env.BASE_URL}/verify-email/${newUser._id}/${emailVerifyToken}`;

    // ✅ Email content
    const html = verifyEmailTemp(verifyUrl, username);

    await sendEmail(email, "Verify Your Email - GigGlobe", html);

    return res.status(201).json({
      success: true,
      message:
        "User registered successfully. A verification email has been sent.",
    });
  } catch (err) {
    console.error("Register error:", err);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
};

export const resendVerificationEmail = async (req, res) => {
  const { email, userId } = req.body;

  if (!email && !userId) {
    return res
      .status(400)
      .json({ success: false, message: "Email or userId is required" });
  }

  try {
    // Find user by email or userId
    const user = email
      ? await User.findOne({ email })
      : await User.findById(userId);

    if (!user) {
      return res
        .status(404)
        .json({ success: false, message: "User not found" });
    }

    if (user.isVerified) {
      return res
        .status(400)
        .json({ success: false, message: "Account already verified" });
    }

    // Generate new token and expiry
    const token = crypto.randomBytes(32).toString("hex");
    const expiry = Date.now() + 60 * 60 * 1000; // 1 hour

    user.emailVerifyToken = token;
    user.emailVerifyExpires = expiry;
    await user.save();

    const verifyUrl = `${process.env.BASE_URL}/verify-email/${user._id}/${token}`;

    const html = verifyEmailTemp(verifyUrl, user.username);

    await sendEmail(user.email, "Verify Your Email - GigGlobe", html);

    return res.status(200).json({
      success: true,
      message: "Verification email resent successfully",
    });
  } catch (err) {
    console.error("Resend verification error:", err);
    return res.status(500).json({
      success: false,
      message: "Server error, please try again later",
    });
  }
};

export const verifyEmail = async (req, res) => {
  const { userId, token } = req.params;

  // Validate input parameters
  if (!userId || !token) {
    return res
      .status(400)
      .json({ success: false, message: "Invalid verification link." });
  }

  try {
    const user = await User.findById(userId);

    if (!user) {
      return res.status(400).json({
        success: false,
        message: "Invalid or expired verification link.",
      });
    }

    // Check if user is already verified
    if (user.isVerified) {
      return res
        .status(200)
        .json({ success: true, message: "Email is already verified!" });
    }

    // Token check
    if (
      user.emailVerifyToken !== token ||
      !user.emailVerifyExpires ||
      user.emailVerifyExpires < Date.now()
    ) {
      return res.status(400).json({
        success: false,
        message: "Verification link is invalid or has expired.",
      });
    }

    // Update user
    user.isVerified = true;
    user.emailVerifyToken = undefined;
    user.emailVerifyExpires = undefined;
    await user.save();

    return res
      .status(200)
      .json({ success: true, message: "Email verified successfully!" });
  } catch (err) {
    console.error("Email verification error:", err);
    res.status(500).json({ success: false, message: "Server error" });
  }
};

export const loginUser = async (req, res) => {
  const { email, password } = req.body;

  // Input validation
  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: "Email and password are required",
    });
  }

  try {
    // Find user and explicitly select the password field
    const user = await User.findOne({ email }).select("+password");

    // User not found
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "Invalid email or password",
      });
    }

    // Check if email is verified
    if (!user.isVerified) {
      return res.status(401).json({
        success: false,
        message: "Please verify your email before logging in",
      });
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: "Invalid email or password",
      });
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate JWT
    const token = jwt.sign(
      { userId: user._id, email: user.email },
      process.env.JWT_SECRET,
      { expiresIn: "7d" }
    );

    // Set token in header
    res.setHeader("Authorization", `Bearer ${token}`);

    // Return success (also include token in response for client-side storage)
    res.status(200).json({
      success: true,
      message: "Login successful",
      token,
      user: {
        id: user._id,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        role: user.role,
        isSeller: user.isSeller,
      },
    });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({ success: false, message: "Server error" });
  }
};

export const forgotPassword = async (req, res) => {
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({
      success: false,
      message: "Email is required",
    });
  }

  try {
    const user = await User.findOne({ email });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User with this email does not exist",
      });
    }

    // Generate reset token
    const token = crypto.randomBytes(32).toString("hex");
    const expiry = Date.now() + 60 * 60 * 1000; // 1 hour

    user.passwordResetToken = token;
    user.passwordResetExpires = expiry;
    await user.save();

    const resetUrl = `${process.env.BASE_URL}/reset-password/${user._id}/${token}`;

    const html = resetPasswordEmailTemp(resetUrl);

    await sendEmail(email, "Reset Your Password - GigGlobe", html);

    res.status(200).json({
      success: true,
      message: "Password reset link sent to your email",
    });
  } catch (err) {
    console.error("Forgot password error:", err);
    res.status(500).json({ success: false, message: "Server error" });
  }
};

export const resetPassword = async (req, res) => {
  const { userId, token } = req.params;
  const { password } = req.body;

  if (!password) {
    return res.status(400).json({
      success: false,
      message: "Password is required",
    });
  }

  try {
    const user = await User.findById(userId);

    if (
      !user ||
      user.passwordResetToken !== token ||
      user.passwordResetExpires < Date.now()
    ) {
      return res.status(400).json({
        success: false,
        message: "Invalid or expired password reset token",
      });
    }

    // Hash and set new password
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(password, salt);

    // Clear reset fields
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;

    await user.save();

    res.status(200).json({
      success: true,
      message: "Password has been reset successfully",
    });
  } catch (err) {
    console.error("Reset password error:", err);
    res.status(500).json({ success: false, message: "Server error" });
  }
};

export const logoutUser = async (_req, res) => {
  try {
    res.status(200).json({
      success: true,
      message: "Logout successful",
    });
  } catch (error) {
    console.error("Logout error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to logout",
    });
  }
};

// Get current user profile
export const getCurrentUser = async (req, res) => {
  try {
    const user = req.user; // Set by verifyToken middleware

    res.status(200).json({
      success: true,
      user: {
        id: user._id,
        username: user.username,
        fullName: user.fullName,
        email: user.email,
        role: user.role,
        isSeller: user.isSeller,
        isVerified: user.isVerified,
      },
    });
  } catch (error) {
    console.error("Get current user error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get user profile",
    });
  }
};
