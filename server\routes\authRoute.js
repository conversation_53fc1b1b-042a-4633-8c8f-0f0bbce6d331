import express from "express";
import {
  registerUser,
  resendVerificationEmail,
  verifyEmail,
  loginUser,
  forgotPassword,
  resetPassword,
  logoutUser,
  getCurrentUser,
} from "../controllers/authController.js";
import {
  loginLimiter,
  forgotPasswordLimiter,
} from "../middlewares/rateLimiter.js";
import { verifyToken } from "../middlewares/verifyToken.js";

const router = express.Router();

router.post("/register", registerUser);
router.post("/resend-verification", resendVerificationEmail);
router.get("/verify-email/:userId/:token", verifyEmail);
router.post("/login", loginLimiter, loginUser);
router.post("/forgot-password", forgotPasswordLimiter, forgotPassword);
router.post("/reset-password/:userId/:token", resetPassword);
router.post("/logout", logoutUser);
router.get("/me", verifyToken, getCurrentUser);

export default router;
