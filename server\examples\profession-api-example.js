// Example usage of the new /profession API endpoint
// This shows how to create a profession with subcategories and icon in one request
// NOTE: This endpoint requires admin authentication via admin_token cookie
// The createdBy field is automatically populated from the authenticated admin user

const exampleRequest = {
  method: 'POST',
  url: '/api/admin/professions',
  headers: {
    'Content-Type': 'application/json',
    // Admin authentication cookie will be included automatically
  },
  body: {
    title: "Web Development",
    subcategories: [
      "Frontend Development",
      "Backend Development", 
      "Full Stack Development",
      "WordPress Development",
      "E-commerce Development"
    ],
    icon: "code" // or icon URL/path
  }
};

// Expected response:
const exampleResponse = {
  success: true,
  profession: {
    _id: "64f8a1b2c3d4e5f6a7b8c9d0",
    name: "Web Development",
    icon: "code",
    createdBy: "64f8a1b2c3d4e5f6a7b8c9e1", // Admin user ID who created this
    createdAt: "2024-01-15T10:30:00.000Z",
    updatedAt: "2024-01-15T10:30:00.000Z",
    __v: 0
  },
  subcategories: [
    {
      _id: "64f8a1b2c3d4e5f6a7b8c9d1",
      name: "Frontend Development",
      profession: "64f8a1b2c3d4e5f6a7b8c9d0",
      createdBy: "64f8a1b2c3d4e5f6a7b8c9e1", // Admin user ID who created this
      createdAt: "2024-01-15T10:30:00.000Z",
      updatedAt: "2024-01-15T10:30:00.000Z",
      __v: 0
    },
    {
      _id: "64f8a1b2c3d4e5f6a7b8c9d2",
      name: "Backend Development",
      profession: "64f8a1b2c3d4e5f6a7b8c9d0",
      createdBy: "64f8a1b2c3d4e5f6a7b8c9e1", // Admin user ID who created this
      createdAt: "2024-01-15T10:30:00.000Z",
      updatedAt: "2024-01-15T10:30:00.000Z",
      __v: 0
    },
    // ... other subcategories
  ]
};

// Error cases:
// 1. Missing title: { message: "Profession title is required" }
// 2. Missing/empty subcategories: { message: "At least one subcategory is required" }
// 3. Profession already exists: { message: "Profession already exists" }

export { exampleRequest, exampleResponse };
