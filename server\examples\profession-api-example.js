// Example usage of the new /profession API endpoint
// This shows how to create a profession with subcategories and icon in one request
// NOTE: This endpoint requires admin authentication via admin_token cookie
// The createdBy field is automatically populated from the authenticated admin user

const exampleRequest = {
  method: 'POST',
  url: '/api/admin/professions',
  headers: {
    'Content-Type': 'application/json',
    // Admin authentication cookie will be included automatically
  },
  body: {
    title: "Web Development",
    subcategories: [
      {
        title: "Languages & Frameworks",
        items: ["JavaScript", "React", "Vue.js", "Angular", "Node.js", "Python", "Django", "Flask"]
      },
      {
        title: "Development Types",
        items: ["Frontend Development", "Backend Development", "Full Stack Development"]
      },
      {
        title: "CMS & E-commerce",
        items: ["WordPress Development", "Shopify Development", "WooCommerce", "Magento"]
      },
      {
        title: "Databases",
        items: ["MySQL", "PostgreSQL", "MongoDB", "Redis"]
      }
    ],
    icon: "code" // or icon URL/path
  }
};

// Expected response:
const exampleResponse = {
  success: true,
  profession: {
    _id: "64f8a1b2c3d4e5f6a7b8c9d0",
    name: "Web Development",
    icon: "code",
    createdBy: "64f8a1b2c3d4e5f6a7b8c9e1", // Admin user ID who created this
    createdAt: "2024-01-15T10:30:00.000Z",
    updatedAt: "2024-01-15T10:30:00.000Z",
    __v: 0
  },
  subcategories: [
    {
      _id: "64f8a1b2c3d4e5f6a7b8c9d1",
      name: "JavaScript",
      title: "Languages & Frameworks",
      profession: "64f8a1b2c3d4e5f6a7b8c9d0",
      createdBy: "64f8a1b2c3d4e5f6a7b8c9e1", // Admin user ID who created this
      createdAt: "2024-01-15T10:30:00.000Z",
      updatedAt: "2024-01-15T10:30:00.000Z",
      __v: 0
    },
    {
      _id: "64f8a1b2c3d4e5f6a7b8c9d2",
      name: "React",
      title: "Languages & Frameworks",
      profession: "64f8a1b2c3d4e5f6a7b8c9d0",
      createdBy: "64f8a1b2c3d4e5f6a7b8c9e1", // Admin user ID who created this
      createdAt: "2024-01-15T10:30:00.000Z",
      updatedAt: "2024-01-15T10:30:00.000Z",
      __v: 0
    },
    {
      _id: "64f8a1b2c3d4e5f6a7b8c9d3",
      name: "Frontend Development",
      title: "Development Types",
      profession: "64f8a1b2c3d4e5f6a7b8c9d0",
      createdBy: "64f8a1b2c3d4e5f6a7b8c9e1", // Admin user ID who created this
      createdAt: "2024-01-15T10:30:00.000Z",
      updatedAt: "2024-01-15T10:30:00.000Z",
      __v: 0
    },
    // ... other subcategories grouped by their titles
  ]
};

// Error cases:
// 1. Missing title: { message: "Profession title is required" }
// 2. Missing/empty subcategories: { message: "At least one subcategory group is required" }
// 3. Invalid subcategory structure: { message: "Each subcategory group must have a title and at least one item" }
// 4. Profession already exists: { message: "Profession already exists" }

// Individual subcategory creation (POST /api/admin/subcategories):
const individualSubcategoryRequest = {
  method: 'POST',
  url: '/api/admin/subcategories',
  body: {
    name: "Flutter",
    title: "Languages & Frameworks",
    professionId: "64f8a1b2c3d4e5f6a7b8c9d0"
  }
};

export { exampleRequest, exampleResponse, individualSubcategoryRequest };
