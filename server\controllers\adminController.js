import Profession from "../models/Profession.js";
import SubCategory from "../models/SubCategory.js";


// POST /api/admin/professions
export const addProfession = async (req, res) => {
  const { name, icon } = req.body;

  try {
    const exists = await Profession.findOne({ name });
    if (exists) return res.status(400).json({ message: "Profession already exists" });

    const profession = new Profession({ name, icon });
    await profession.save();

    res.status(201).json({ success: true, profession });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

// POST /api/admin/subcategories
export const addSubcategory = async (req, res) => {
  const { name, professionId } = req.body;

  try {
    const profession = await Profession.findById(professionId);
    if (!profession) return res.status(404).json({ message: "Profession not found" });

    const subcategory = new SubCategory({ name, profession: professionId });
    await subcategory.save();

    res.status(201).json({ success: true, subcategory });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

