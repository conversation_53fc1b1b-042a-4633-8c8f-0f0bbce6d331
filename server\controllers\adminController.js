import Profession from "../models/Profession.js";
import SubCategory from "../models/SubCategory.js";


// POST /api/admin/professions
export const addProfession = async (req, res) => {
  const { title, subcategories, icon } = req.body;

  try {
    // Validate required fields
    if (!title) {
      return res.status(400).json({ message: "Profession title is required" });
    }

    if (!subcategories || !Array.isArray(subcategories) || subcategories.length === 0) {
      return res.status(400).json({ message: "At least one subcategory is required" });
    }

    // Check if profession already exists
    const exists = await Profession.findOne({ name: title });
    if (exists) {
      return res.status(400).json({ message: "Profession already exists" });
    }

    // Create the profession
    const profession = new Profession({ name: title, icon });
    await profession.save();

    // Create subcategories
    const subcategoryPromises = subcategories.map(subcategoryName => {
      const subcategory = new SubCategory({
        name: subcategoryName,
        profession: profession._id
      });
      return subcategory.save();
    });

    const createdSubcategories = await Promise.all(subcategoryPromises);

    res.status(201).json({
      success: true,
      profession,
      subcategories: createdSubcategories
    });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

// POST /api/admin/subcategories
export const addSubcategory = async (req, res) => {
  const { name, professionId } = req.body;

  try {
    const profession = await Profession.findById(professionId);
    if (!profession) return res.status(404).json({ message: "Profession not found" });

    const subcategory = new SubCategory({ name, profession: professionId });
    await subcategory.save();

    res.status(201).json({ success: true, subcategory });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

