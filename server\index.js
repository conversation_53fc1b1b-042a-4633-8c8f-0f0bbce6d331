import express from "express";
import mongoose from "mongoose";
import dotenv from "dotenv";
import cors from "cors";
import cookieParser from "cookie-parser";

// Load env vars FIRST before importing any modules that use them
dotenv.config();

// Import routes AFTER dotenv is configured
import authRoutes from "./routes/authRoute.js";
import adminAuthRoute from "./routes/adminAuthRoute.js";
import adminRoute from "./routes/adminRoute.js";

const app = express();

// Middleware
app.use(
  cors({
    origin: [
      "http://localhost:5173",
      "http://localhost:5174",
      "http://localhost:3000",
    ], // Vite dev server and any other ports
    credentials: true,
    exposedHeaders: ["Authorization"], // Allow client to read Authorization header
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);
app.use(express.json());
app.use(cookieParser());

// Routes
app.use("/api/auth", authRoutes);
app.use("/api/auth/admin", adminAuthRoute);
app.use("/api/admin", adminRoute);

// Default route
app.get("/", (req, res) => {
  res.send("GigGlobe API is running...");
});

// Connect to MongoDB and start server
const PORT = process.env.PORT || 5000;
const MONGO_URI = process.env.MONGO_URI;

mongoose
  .connect(MONGO_URI)
  .then(() => {
    console.log("✅ MongoDB connected");
    app.listen(PORT, () => {
      console.log(`🚀 Server running at http://localhost:${PORT}`);
    });
  })
  .catch((err) => {
    console.error("❌ MongoDB connection error:", err);
  });
