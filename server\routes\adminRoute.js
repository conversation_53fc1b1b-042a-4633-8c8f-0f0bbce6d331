// routes/adminRoute.js
import express from "express";
import verifyAdminMiddleware from "../middlewares/verifyAdminMiddleware.js";
import { addProfession, addSubcategory } from "../controllers/adminController.js";

const router = express.Router();

router.post("/professions", verifyAdminMiddleware, addProfession);
// Keep the subcategory route for individual subcategory additions if needed
router.post("/subcategories", verifyAdminMiddleware, addSubcategory);

export default router;
