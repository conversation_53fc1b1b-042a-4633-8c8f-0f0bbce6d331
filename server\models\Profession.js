// models/Profession.js
import mongoose from "mongoose";

const ProfessionSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
  },
  icon: String, // optional: icon name or URL
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
}, {
  timestamps: true, // adds createdAt and updatedAt fields
});

export default mongoose.model("Profession", ProfessionSchema);
