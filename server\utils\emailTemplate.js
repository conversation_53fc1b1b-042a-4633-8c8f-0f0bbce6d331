const verifyEmailTemp = (verifyUrl, username) => {
  return `
    <div style="background-color: #f3f4f6; font-family: Arial, Helvetica, sans-serif; padding: 40px 0;">
        <div style="max-width: 500px; margin: 0 auto; background: #fff; border-radius: 16px; box-shadow: 0 4px 24px rgba(0,0,0,0.08); overflow: hidden;">
            <div style="padding: 32px; text-align: center;">
                <img src="https://img.icons8.com/ios/50/mail.png" alt="Email Icon" style="display: block; margin: 0 auto 20px; width: 48px; height: 48px;" />
                <h1 style="font-size: 24px; font-weight: bold; color: #111827; margin-bottom: 12px;">Verify your email</h1>
                <p style="color: #444; font-size: 16px; margin-bottom: 24px;">
                    Hi <span style="font-weight: 600; color: #4f46e5;">${username}</span>,<br><br>
                    Thank you for signing up to <span style="font-weight: 600; color: #4f46e5;">GigGlobe</span>.<br>
                    To complete your registration, please verify your email by clicking the button below.
                </p>
                <a href="${verifyUrl}" target="_blank" style="display: inline-block; background-color: #4f46e5; color: #fff; text-decoration: none; padding: 14px 32px; border-radius: 8px; font-weight: 600; font-size: 16px; margin-top: 24px;">
                    Verify Email
                </a>
            </div>
            <div style="padding: 20px 32px; text-align: center; background: #f9fafb;">
                <p style="color: #6b7280; font-size: 14px; margin-bottom: 8px;">
                    If you didn’t create an account, you can safely ignore this email.
                </p>
                <p style="color: #bdbdbd; font-size: 12px; margin-top: 10px;">
                    &copy; ${new Date().getFullYear()} GigGlobe, All rights reserved.
                </p>
            </div>
        </div>
    </div>
`;
};

const resetPasswordEmailTemp = (resetUrl) => {
  return `<div style="background-color: #f3f4f6; font-family: Arial, Helvetica, sans-serif; padding: 40px 0;">
        <div style="max-width: 500px; margin: 0 auto; background: #fff; border-radius: 16px; box-shadow: 0 4px 24px rgba(0,0,0,0.08); overflow: hidden;">
            <div style="padding: 32px; text-align: center;">
                <img src="https://img.icons8.com/pulsar-line/50/password.png" alt="Email Icon" style="display: block; margin: 0 auto 20px; width: 48px; height: 48px;" />
                <h1 style="font-size: 24px; font-weight: bold; color: #111827; margin-bottom: 12px;">Reset Your Password</h1>
                <p style="color: #444; font-size: 16px; margin-bottom: 24px;">
                   <span style="font-weight: 600; color: #4f46e5;">GigGlobe</span>.<br>
                    To reset your password, please click the button below.
                </p>
                <a href="${resetUrl}" target="_blank" style="display: inline-block; background-color: #4f46e5; color: #fff; text-decoration: none; padding: 14px 32px; border-radius: 8px; font-weight: 600; font-size: 16px; margin-top: 24px;">
                    Reset Password
                </a>
            </div>
            <div style="padding: 20px 32px; text-align: center; background: #f9fafb;">
                <p style="color: #6b7280; font-size: 14px; margin-bottom: 8px;">
                    If you didn’t request a password reset, you can safely ignore this email.
                </p>
                <p style="color: #bdbdbd; font-size: 12px; margin-top: 10px;">
                    &copy; ${new Date().getFullYear()} GigGlobe, All rights reserved.
                </p>
            </div>
        </div>
    </div>`;
};

const adminLoginOTPTemp = (otp) => {
  return `
    <div style="background-color: #f3f4f6; font-family: Arial, Helvetica, sans-serif; padding: 40px 0;">
        <div style="max-width: 500px; margin: 0 auto; background: #fff; border-radius: 16px; box-shadow: 0 4px 24px rgba(0,0,0,0.08); overflow: hidden;">
            <div style="padding: 32px; text-align: center;">
                <img src="https://img.icons8.com/ios/50/key.png" alt="Key Icon" style="display: block; margin: 0 auto 20px; width: 48px; height: 48px;" />
                <h1 style="font-size: 24px; font-weight: bold; color: #111827; margin-bottom: 12px;">Admin Login OTP</h1>
                <p style="color: #444; font-size: 16px; margin-bottom: 24px;">
                    Your One-Time Password for admin login is:
                </p>
                <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 24px 0;">
                    <span style="font-size: 32px; font-weight: bold; color: #4f46e5; letter-spacing: 4px;">${otp}</span>
                </div>
                <p style="color: #6b7280; font-size: 14px;">
                    This OTP will expire in 10 minutes.
                </p>
            </div>
            <div style="padding: 20px 32px; text-align: center; background: #f9fafb;">
                <p style="color: #6b7280; font-size: 14px; margin-bottom: 8px;">
                    If you didn't request this OTP, please ignore this email.
                </p>
                <p style="color: #bdbdbd; font-size: 12px; margin-top: 10px;">
                    &copy; ${new Date().getFullYear()} GigGlobe, All rights reserved.
                </p>
            </div>
        </div>
    </div>
  `;
};

export {
  verifyEmailTemp,
  resetPasswordEmailTemp,
  adminLoginOTPTemp,
};
