import User from "../models/User.js";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import otpGenerator from "../utils/otpGenerator.js";
import { adminLoginOTPTemp } from "../utils/emailTemplate.js";
import { sendEmail } from "../utils/mailer.js";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret";

export const adminLogin = async (req, res) => {
  try {
    const { email, password } = req.body;
    const admin = await User.findOne({ email, role: "admin" }).select(
      "+password"
    );

    if (!admin || !(await bcrypt.compare(password, admin.password))) {
      return res.status(401).json({ message: "Invalid credentials" });
    }

    // Check cooldown
    const now = new Date();
    const hourAgo = new Date(now - 60 * 60 * 1000);
    if (admin.otp?.lastRequestedAt > hourAgo && admin.otp?.requestCount >= 5) {
      return res
        .status(429)
        .json({ message: "Too many OTP requests. Try again later." });
    }

    if (
      admin.otp?.lastRequestedAt &&
      now - admin.otp.lastRequestedAt < 60 * 1000
    ) {
      return res
        .status(429)
        .json({ message: "Please wait 1 minute before next OTP request." });
    }

    // Generate OTP
    const otp = otpGenerator(6);
    admin.otp = {
      code: otp,
      expiresAt: new Date(now.getTime() + 10 * 60 * 1000), // expires in 10 mins
      lastRequestedAt: now,
      requestCount:
        admin.otp?.lastRequestedAt > hourAgo ? admin.otp.requestCount + 1 : 1,
      attempts: 0,
    };
    await admin.save();

    const html = adminLoginOTPTemp(otp);

    // Send OTP via email with error handling
    try {
      await sendEmail(email, "Admin Login OTP - GigGlobe", html);
    } catch (emailError) {
      console.error("Failed to send OTP email:", emailError);
      // Rollback OTP generation since email failed
      admin.otp = undefined;
      await admin.save();
      return res.status(500).json({
        message: "Failed to send OTP email. Please try again later.",
        error:
          process.env.NODE_ENV === "development"
            ? emailError.message
            : undefined,
      });
    }

    return res
      .status(200)
      .json({ message: "Login successful. OTP sent to email." });
  } catch (error) {
    console.error("Admin login error:", error);
    return res.status(500).json({
      message: "Internal server error",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

export const verifyOtp = async (req, res) => {
  const { email, otp } = req.body;

  if (!email || !otp) {
    return res.status(400).json({ message: "Email and OTP are required." });
  }

  const admin = await User.findOne({ email, role: "admin" });

  if (!admin || !admin.otp || !admin.otp.code) {
    return res
      .status(401)
      .json({ message: "OTP not found. Please login again." });
  }

  if (admin.otp.expiresAt < new Date()) {
    return res
      .status(401)
      .json({ message: "OTP expired. Please login again." });
  }

  if (admin.otp.attempts >= 5) {
    return res
      .status(403)
      .json({ message: "Too many invalid attempts. Please login again." });
  }

  if (otp !== admin.otp.code) {
    admin.otp.attempts += 1;
    await admin.save();
    return res.status(401).json({ message: "Invalid OTP." });
  }

  // Clear OTP
  admin.otp = undefined;
  await admin.save();

  // Generate JWT
  const token = jwt.sign(
    { userId: admin._id, role: admin.role },
    process.env.JWT_SECRET,
    { expiresIn: "1d" }
  );

  // Send JWT as secure HTTP-only cookie
  res.cookie("admin_token", token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production", // only send over HTTPS in prod
    sameSite: "strict", // CSRF protection
    maxAge: 24 * 60 * 60 * 1000, // 1 day
  });

  res.status(200).json({ message: "OTP verified. Logged in successfully." });
};

export const adminLogout = (req, res) => {
  res.clearCookie("admin_token", {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  });

  res.status(200).json({ message: "Logged out successfully." });
};
